# How to contribute changes

This is a learning repository, students are expected to submit their work in individual branches.

- Your primary branch must be named: `student/<your-email-username>`
  - Example: for `<EMAIL>`, the branch is `student/first.last`.
- For work in progress, you may create temporary feature branches: `student/<your-email-username>/<feature-name>`
  - Example: `student/first.last/some-new-feature`.
- Do not fork the repository. Do not push to `main` or to any other student’s branch.
