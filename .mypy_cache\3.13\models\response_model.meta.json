{"data_mtime": 1758195863, "dep_lines": [1, 1, 1, 1, 1], "dep_prios": [5, 30, 30, 30, 5], "dependencies": ["builtins", "_frozen_importlib", "abc", "typing"], "hash": "b45d04f27bfe4aff3b0f2583fadf4ff26d16a771", "id": "models.response_model", "ignore_all": true, "interface_hash": "4d2034df03a92a631e72763826cb9a74c147eb68", "mtime": 1758195717, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\DOJO\\ai-application-developer-labs\\backend\\models\\response_model.py", "plugin_data": null, "size": 140, "suppressed": ["pydantic"], "version_id": "1.15.0"}