{".class": "MypyFile", "_fullname": "os.path", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "__all__": {".class": "SymbolTableNode", "cross_ref": "ntpath.__all__", "kind": "Gdef", "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "os.path.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "os.path.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "os.path.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "os.path.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "os.path.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "os.path.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abspath": {".class": "SymbolTableNode", "cross_ref": "posixpath.abspath", "kind": "Gdef"}, "altsep": {".class": "SymbolTableNode", "cross_ref": "ntpath.altsep", "kind": "Gdef"}, "basename": {".class": "SymbolTableNode", "cross_ref": "posixpath.basename", "kind": "Gdef"}, "commonpath": {".class": "SymbolTableNode", "cross_ref": "posixpath.commonpath", "kind": "Gdef"}, "commonprefix": {".class": "SymbolTableNode", "cross_ref": "genericpath.commonprefix", "kind": "Gdef"}, "curdir": {".class": "SymbolTableNode", "cross_ref": "posixpath.curdir", "kind": "Gdef"}, "defpath": {".class": "SymbolTableNode", "cross_ref": "posixpath.defpath", "kind": "Gdef"}, "devnull": {".class": "SymbolTableNode", "cross_ref": "posixpath.devnull", "kind": "Gdef"}, "dirname": {".class": "SymbolTableNode", "cross_ref": "posixpath.dirname", "kind": "Gdef"}, "exists": {".class": "SymbolTableNode", "cross_ref": "genericpath.exists", "kind": "Gdef"}, "expanduser": {".class": "SymbolTableNode", "cross_ref": "posixpath.expanduser", "kind": "Gdef"}, "expandvars": {".class": "SymbolTableNode", "cross_ref": "posixpath.expandvars", "kind": "Gdef"}, "extsep": {".class": "SymbolTableNode", "cross_ref": "posixpath.extsep", "kind": "Gdef"}, "getatime": {".class": "SymbolTableNode", "cross_ref": "genericpath.getatime", "kind": "Gdef"}, "getctime": {".class": "SymbolTableNode", "cross_ref": "genericpath.getctime", "kind": "Gdef"}, "getmtime": {".class": "SymbolTableNode", "cross_ref": "genericpath.getmtime", "kind": "Gdef"}, "getsize": {".class": "SymbolTableNode", "cross_ref": "genericpath.getsize", "kind": "Gdef"}, "isabs": {".class": "SymbolTableNode", "cross_ref": "posixpath.isabs", "kind": "Gdef"}, "isdevdrive": {".class": "SymbolTableNode", "cross_ref": "genericpath.isdevdrive", "kind": "Gdef"}, "isdir": {".class": "SymbolTableNode", "cross_ref": "genericpath.isdir", "kind": "Gdef"}, "isfile": {".class": "SymbolTableNode", "cross_ref": "genericpath.isfile", "kind": "Gdef"}, "isjunction": {".class": "SymbolTableNode", "cross_ref": "posixpath.isjunction", "kind": "Gdef"}, "islink": {".class": "SymbolTableNode", "cross_ref": "posixpath.islink", "kind": "Gdef"}, "ismount": {".class": "SymbolTableNode", "cross_ref": "posixpath.ismount", "kind": "Gdef"}, "isreserved": {".class": "SymbolTableNode", "cross_ref": "ntpath.isreserved", "kind": "Gdef"}, "join": {".class": "SymbolTableNode", "cross_ref": "ntpath.join", "kind": "Gdef"}, "lexists": {".class": "SymbolTableNode", "cross_ref": "posixpath.lexists", "kind": "Gdef"}, "normcase": {".class": "SymbolTableNode", "cross_ref": "posixpath.normcase", "kind": "Gdef"}, "normpath": {".class": "SymbolTableNode", "cross_ref": "posixpath.normpath", "kind": "Gdef"}, "pardir": {".class": "SymbolTableNode", "cross_ref": "posixpath.pardir", "kind": "Gdef"}, "pathsep": {".class": "SymbolTableNode", "cross_ref": "posixpath.pathsep", "kind": "Gdef"}, "realpath": {".class": "SymbolTableNode", "cross_ref": "ntpath.realpath", "kind": "Gdef"}, "relpath": {".class": "SymbolTableNode", "cross_ref": "posixpath.relpath", "kind": "Gdef"}, "samefile": {".class": "SymbolTableNode", "cross_ref": "genericpath.samefile", "kind": "Gdef"}, "sameopenfile": {".class": "SymbolTableNode", "cross_ref": "genericpath.sameopenfile", "kind": "Gdef"}, "samestat": {".class": "SymbolTableNode", "cross_ref": "genericpath.samestat", "kind": "Gdef"}, "sep": {".class": "SymbolTableNode", "cross_ref": "posixpath.sep", "kind": "Gdef"}, "split": {".class": "SymbolTableNode", "cross_ref": "posixpath.split", "kind": "Gdef"}, "splitdrive": {".class": "SymbolTableNode", "cross_ref": "posixpath.splitdrive", "kind": "Gdef"}, "splitext": {".class": "SymbolTableNode", "cross_ref": "posixpath.splitext", "kind": "Gdef"}, "splitroot": {".class": "SymbolTableNode", "cross_ref": "posixpath.splitroot", "kind": "Gdef"}, "supports_unicode_filenames": {".class": "SymbolTableNode", "cross_ref": "posixpath.supports_unicode_filenames", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\os\\path.pyi"}